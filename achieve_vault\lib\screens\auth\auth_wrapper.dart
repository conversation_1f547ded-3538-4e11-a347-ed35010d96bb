import 'package:flutter/material.dart';
import '../../services/local_auth_service.dart';
import 'login_screen.dart';
import '../home/<USER>';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  final _authService = LocalAuthService();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    await _authService.initialize();
    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Skip authentication - go straight to home screen for demo
    return const HomeScreen();
  }
}
